"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteUserProfile = exports.updateUserProfile = exports.getUserProfile = void 0;
const http_status_codes_1 = require("http-status-codes");
const User_1 = __importDefault(require("../models/User"));
const Child_1 = __importDefault(require("../models/Child"));
// Get user profile (parent)
const getUserProfile = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.userIdFromToken;
        const user = yield User_1.default.findById(userId);
        if (!user) {
            res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        res.status(http_status_codes_1.StatusCodes.OK).json({
            success: true,
            message: 'Profile retrieved successfully',
            user: {
                id: user._id.toString(),
                fullname: user.fullname,
                username: user.username,
                email: user.email,
                phoneNumber: user.phoneNumber,
                role: user.role,
                isVerified: user.isVerified,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            }
        });
    }
    catch (error) {
        console.error('Get user profile error:', error);
        res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Internal server error while retrieving profile'
        });
    }
});
exports.getUserProfile = getUserProfile;
// Update user profile
const updateUserProfile = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.userIdFromToken;
        const { fullname, email, phoneNumber } = req.body;
        const user = yield User_1.default.findById(userId);
        if (!user) {
            res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        // Check if email is being updated and if it already exists
        if (email && email !== user.email) {
            const existingUser = yield User_1.default.findOne({
                email: email,
                _id: { $ne: userId }
            });
            if (existingUser) {
                res.status(http_status_codes_1.StatusCodes.CONFLICT).json({
                    success: false,
                    message: 'Email already exists'
                });
                return;
            }
        }
        // Update user data
        if (fullname)
            user.fullname = fullname;
        if (email)
            user.email = email;
        if (phoneNumber)
            user.phoneNumber = phoneNumber;
        yield user.save();
        res.status(http_status_codes_1.StatusCodes.OK).json({
            success: true,
            message: 'Profile updated successfully',
            user: {
                id: user._id.toString(),
                fullname: user.fullname,
                username: user.username,
                email: user.email,
                phoneNumber: user.phoneNumber,
                role: user.role,
                isVerified: user.isVerified,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            }
        });
    }
    catch (error) {
        console.error('Update user profile error:', error);
        if (error.code === 11000) {
            res.status(http_status_codes_1.StatusCodes.CONFLICT).json({
                success: false,
                message: 'Email already exists'
            });
            return;
        }
        res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Internal server error while updating profile'
        });
    }
});
exports.updateUserProfile = updateUserProfile;
// Delete user profile (with password confirmation and cascade delete children)
const deleteUserProfile = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.userIdFromToken;
        const { password } = req.body;
        if (!password) {
            res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Password is required for account deletion'
            });
            return;
        }
        const user = yield User_1.default.findById(userId);
        if (!user) {
            res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        // Verify password
        const isPasswordValid = yield user.comparePassword(password);
        if (!isPasswordValid) {
            res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                success: false,
                message: 'Invalid password'
            });
            return;
        }
        // Delete all children of this parent first (cascade delete)
        yield Child_1.default.deleteMany({ parent: userId });
        // Delete the user
        yield User_1.default.findByIdAndDelete(userId);
        res.status(http_status_codes_1.StatusCodes.OK).json({
            success: true,
            message: 'Profile and all associated children deleted successfully'
        });
    }
    catch (error) {
        console.error('Delete user profile error:', error);
        res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Internal server error while deleting profile'
        });
    }
});
exports.deleteUserProfile = deleteUserProfile;
