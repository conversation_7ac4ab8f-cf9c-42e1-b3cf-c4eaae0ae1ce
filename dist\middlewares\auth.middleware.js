"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyTokenForParent = exports.verifyTokenForAdmin = exports.verifyToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const http_status_codes_1 = require("http-status-codes");
const User_1 = __importDefault(require("../models/User"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const verifyToken = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    const authHeader = req.headers.authorization;
    if (authHeader) {
        const token = authHeader.split(" ")[1];
        if (!token) {
            res
                .status(http_status_codes_1.StatusCodes.UNAUTHORIZED)
                .json({ message: "Access Denied Invalid Token" });
            return;
        }
        jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || "", (err, decoded) => __awaiter(void 0, void 0, void 0, function* () {
            if (err) {
                res
                    .status(http_status_codes_1.StatusCodes.UNAUTHORIZED)
                    .json({ message: "Token not matched" });
                return;
            }
            const checkUser = yield User_1.default.findById(decoded === null || decoded === void 0 ? void 0 : decoded._id);
            if (!checkUser) {
                res
                    .status(http_status_codes_1.StatusCodes.UNAUTHORIZED)
                    .json({ message: "User not found" });
                return;
            }
            if (!checkUser.isVerified) {
                res
                    .status(http_status_codes_1.StatusCodes.UNAUTHORIZED)
                    .json({ message: "Account not verified" });
                return;
            }
            const user = decoded;
            req.user = user;
            req.userIdFromToken = user === null || user === void 0 ? void 0 : user._id;
            next();
        }));
    }
    else {
        res
            .status(http_status_codes_1.StatusCodes.UNAUTHORIZED)
            .json({ message: "Access Denied Invalid Token" });
        return;
    }
});
exports.verifyToken = verifyToken;
const verifyTokenForAdmin = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    (0, exports.verifyToken)(req, res, () => __awaiter(void 0, void 0, void 0, function* () {
        const user = req.user;
        if ((user === null || user === void 0 ? void 0 : user.role) !== "admin") {
            return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                message: "You are not authorized to access this resource",
            });
        }
        next();
    }));
});
exports.verifyTokenForAdmin = verifyTokenForAdmin;
const verifyTokenForParent = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    (0, exports.verifyToken)(req, res, () => __awaiter(void 0, void 0, void 0, function* () {
        const user = req.user;
        if ((user === null || user === void 0 ? void 0 : user.role) !== "Parent") {
            return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                message: "You are not authorized to access this resource",
            });
        }
        next();
    }));
});
exports.verifyTokenForParent = verifyTokenForParent;
// export const verifyTokenForAdminOrBusiness = async (
//   req: Request,
//   res: Response,
//   next: NextFunction
// ) => {
//   verifyToken(req, res, async () => {
//     const user = req.user as UserPayload;
//     if (user?.role !== "admin" && user?.role !== "business") {
//       return res.status(StatusCodes.UNAUTHORIZED).json({
//         message: "You are not authorized to access this resource",
//       });
//     }
//     next();
//   });
// };
