import { Request, Response, NextFunction } from "express";
import Jwt from "jsonwebtoken";
import { StatusCodes } from "http-status-codes";
import User from "../models/User";
import { UserPayload } from "../types/express";
import dotenv from "dotenv";
dotenv.config();

export const verifyToken = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const authHeader = req.headers.authorization;
  if (authHeader) {
    const token = authHeader.split(" ")[1];
    if (!token) {
      res
        .status(StatusCodes.UNAUTHORIZED)
        .json({ message: "Access Denied Invalid Token" });
      return;
    }
    Jwt.verify(
      token,
      process.env.JWT_SECRET || "",
      async (err: any, decoded: any) => {
        if (err) {
          res
            .status(StatusCodes.UNAUTHORIZED)
            .json({ message: "Token not matched" });
          return;
        }
        console.log('decoded in verifyToken', decoded?.userId);
        const checkUser = await User.findById(decoded?.userId);
        if (!checkUser) {
          res
            .status(StatusCodes.UNAUTHORIZED)
            .json({ message: "User not found" });
          return;
        }
        if (!checkUser.isVerified) {
          res
            .status(StatusCodes.UNAUTHORIZED)
            .json({ message: "Account not verified" });
          return;
        }
        const user = decoded as UserPayload;
        req.user = user;
        req.userIdFromToken = user?._id;
        next();
      }
    );
  } else {
    res
      .status(StatusCodes.UNAUTHORIZED)
      .json({ message: "Access Denied Invalid Token" });
    return;
  }
};

export const verifyTokenForAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  verifyToken(req, res, async () => {
    const user = req.user as UserPayload;
    if (user?.role !== "admin") {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        message: "You are not authorized to access this resource",
      });
    }
    next();
  });
};

export const verifyTokenForParent = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  verifyToken(req, res, async () => {
    const user = req.user as UserPayload;
    console.log('user in verifyTokenForParent', user.role);
    if (user?.role !== "Parent") {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        message: "You are not authorized to access this resource",
      });
    }
    next();
  });
};

// export const verifyTokenForAdminOrBusiness = async (
//   req: Request,
//   res: Response,
//   next: NextFunction
// ) => {
//   verifyToken(req, res, async () => {
//     const user = req.user as UserPayload;
//     if (user?.role !== "admin" && user?.role !== "business") {
//       return res.status(StatusCodes.UNAUTHORIZED).json({
//         message: "You are not authorized to access this resource",
//       });
//     }
//     next();
//   });
// };
