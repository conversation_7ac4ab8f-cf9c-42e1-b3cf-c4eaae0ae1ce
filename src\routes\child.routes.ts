import { Router } from 'express';
import {
  childLogin, 
  getAllChildren, 
} from '../controllers/index';
import { verifyToken } from '../middlewares/auth.middleware';

const router = Router();

// All child routes require authentication
router.use(verifyToken);


router.post('/login', childLogin);
router.get('/get-all-children', getAllChildren);               // GET /api/child - Get all children of parent

export default router;
