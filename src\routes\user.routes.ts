import { Router } from 'express';
import {
  getUserProfile,
  updateUserProfile,
  deleteUserProfile,
  addChild,
  getAllChildren,
  getChildDetails,
  updateChild,
  deleteChild
} from '../controllers/index';
import { verifyToken, verifyTokenForParent } from '../middlewares/auth.middleware';

const router = Router();

// All user routes require authentication
router.use(verifyToken);


// -------- Parent API's ----------------------
router.get('/profile', getUserProfile);        
router.put('/profile', updateUserProfile);     
router.delete('/profile', deleteUserProfile);  



//--------- Child API's ----------------------
router.post('/add-child', addChild);                    
router.get('/get-all-children', getAllChildren);              
router.get('/:childId', getChildDetails); 
router.put('/:childId', updateChild);          
router.delete('/:childId', deleteChild);       

export default router;

