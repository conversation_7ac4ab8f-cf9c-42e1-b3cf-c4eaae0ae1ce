"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const db_1 = __importDefault(require("./config/db"));
const cors_1 = __importDefault(require("cors"));
const body_parser_1 = __importDefault(require("body-parser"));
const helmet_1 = __importDefault(require("helmet"));
const rateLimit_1 = require("./utils/rateLimit");
const routes_1 = require("./routes");
// ----------- Config -------------------
dotenv_1.default.config();
// ----------- Server -------------------
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
const port = process.env.PORT || 5001;
// ---------- Middlewares ----------------------------
app.use(body_parser_1.default.json({ limit: "50mb" }));
app.use(body_parser_1.default.urlencoded({ limit: "50mb", extended: true }));
app.use((0, cors_1.default)());
app.use((0, helmet_1.default)());
app.use((0, rateLimit_1.rateLimiter)());
// Connect to MongoDB
(0, db_1.default)();
// ----------- Routes -------------------
app.use('/api/auth', routes_1.authRouter);
app.use('/api/child', routes_1.childRouter);
app.use('/api/user', routes_1.userRouter);
// Start server
server.listen(port, () => {
    console.log('Server is running on port 5001');
});
