"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const index_1 = require("../controllers/index");
const OTP_1 = require("../controllers/helper/OTP");
const router = (0, express_1.Router)();
// Authentication Routes
router.post('/signup', index_1.signup);
router.post('/verify-signup-otp', OTP_1.verifySignupOTP);
router.post('/resend-otp', OTP_1.resetPasswordGenerateOTP); // for both signup and forgot password
router.post('/login', index_1.login);
// Password Reset Routes
router.post('/forgot-password', OTP_1.resetPasswordGenerateOTP);
router.post('/forgot-password/verify-otp', OTP_1.verifyOtp);
router.post('/forgot-password/reset', index_1.forgotPassword);
exports.default = router;
