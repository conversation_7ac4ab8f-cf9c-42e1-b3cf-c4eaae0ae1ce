"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.forgotPassword = exports.login = exports.signup = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const http_status_codes_1 = require("http-status-codes");
const User_1 = __importDefault(require("../models/User"));
const OTP_1 = require("./helper/OTP");
const validation_1 = require("../utils/validation");
const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
    throw new Error('JWT_SECRET is not defined in the environment variables');
}
// Signup Controller
const signup = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const signupData = req.body;
        // Validate input data
        const validation = (0, validation_1.validateSignupData)(signupData);
        if (!validation.isValid) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                success: false,
                message: validation.errors.join(', ')
            });
        }
        // Sanitize input data
        const { fullname, username, email, phoneNumber, password } = {
            fullname: (0, validation_1.sanitizeInput)(signupData.fullname),
            username: (0, validation_1.sanitizeInput)(signupData.username.toLowerCase()),
            email: (0, validation_1.sanitizeInput)(signupData.email.toLowerCase()),
            phoneNumber: (0, validation_1.sanitizeInput)(signupData.phoneNumber),
            password: signupData.password,
        };
        // console.log('password in signup function', password);
        // const hashedPassword = await bcrypt.hash(password, 10);
        // Check if user already exists
        const existingUser = yield User_1.default.findOne({
            $or: [{ email }, { username }]
        });
        if (existingUser) {
            return res.status(http_status_codes_1.StatusCodes.CONFLICT).json({
                success: false,
                message: existingUser.email === email ? 'Email already exists' : 'Username already exists'
            });
        }
        // Create new user
        const newUser = new User_1.default({
            fullname,
            username: username.toLowerCase(),
            email: email.toLowerCase(),
            phoneNumber,
            password,
            role: 'Parent', // Default role
            isVerified: false
        });
        yield newUser.save();
        // Generate OTP for email verification
        const otp = Math.floor(1000 + Math.random() * 9000).toString();
        // Send OTP email
        yield (0, OTP_1.sendOtpEmail)(email, otp);
        // Store OTP in user document
        newUser.otp = otp;
        newUser.otpCreatedAt = new Date();
        yield newUser.save();
        return res.status(http_status_codes_1.StatusCodes.CREATED).json({
            success: true,
            message: 'User created successfully. Please verify your email with the OTP sent to your email address.',
            user: {
                id: newUser._id,
                fullname: newUser.fullname,
                username: newUser.username,
                email: newUser.email,
                isVerified: newUser.isVerified
            }
        });
    }
    catch (error) {
        console.error('Signup error:', error);
        // Handle mongoose validation errors
        if (error.name === 'ValidationError') {
            const messages = Object.values(error.errors).map((err) => err.message);
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                success: false,
                message: messages.join(', ')
            });
        }
        // Handle duplicate key errors
        if (error.code === 11000) {
            const field = Object.keys(error.keyPattern)[0];
            return res.status(http_status_codes_1.StatusCodes.CONFLICT).json({
                success: false,
                message: `${field} already exists`
            });
        }
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Internal server error during signup'
        });
    }
});
exports.signup = signup;
// Login Controller
const login = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { username, password } = req.body;
        if (!username || !password) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Username and password are required'
            });
        }
        // Find user by username
        const user = yield User_1.default.findOne({ username: username.toLowerCase() });
        if (!user) {
            return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                success: false,
                message: 'Invalid credentials or user not found'
            });
        }
        console.log('password in login function', password);
        console.log('username in login function', username);
        console.log('user.password in login function', user.password);
        // Check password
        const isMatch = yield bcryptjs_1.default.compare(password, user.password);
        console.log('isPasswordValid in login function', isMatch);
        if (!isMatch) {
            return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                success: false,
                message: 'Password is incorrect'
            });
        }
        // Check if user is verified
        if (!user.isVerified) {
            return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                success: false,
                message: 'Please verify your email before logging in'
            });
        }
        // Generate JWT token
        const payload = { userId: user._id, email: user.email };
        const accessToken = jsonwebtoken_1.default.sign(payload, JWT_SECRET, { expiresIn: '30d' });
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            success: true,
            message: 'Login successful',
            user: {
                id: user._id,
                fullname: user.fullname,
                username: user.username,
                email: user.email,
                phoneNumber: user.phoneNumber,
                role: user.role,
                isVerified: user.isVerified
            },
            accessToken
        });
    }
    catch (error) {
        console.error('Login error:', error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Internal server error during login'
        });
    }
});
exports.login = login;
// Forgot Password Controller
const forgotPassword = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email, password, confirmPassword } = req.body;
        if (!email || !password || !confirmPassword) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Email, password, and confirm password are required'
            });
        }
        const user = yield User_1.default.findOne({ email });
        if (!user) {
            return res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'User not found'
            });
        }
        if (!user.isVerified) {
            return res.status(http_status_codes_1.StatusCodes.UNAUTHORIZED).json({
                success: false,
                message: 'Please verify your email before resetting password'
            });
        }
        const isSamePassword = yield bcryptjs_1.default.compare(password, user.password);
        if (isSamePassword) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'New password cannot be the same as the old password'
            });
        }
        if (password !== confirmPassword) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Passwords do not match'
            });
        }
        if (password.length < 6) {
            return res.status(http_status_codes_1.StatusCodes.BAD_REQUEST).json({
                success: false,
                message: 'Password must be at least 6 characters long'
            });
        }
        user.password = password;
        yield user.save();
        return res.status(http_status_codes_1.StatusCodes.OK).json({
            success: true,
            message: 'Password reset successfully'
        });
    }
    catch (error) {
        console.error('Forgot password error:', error);
        return res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Internal server error during password reset'
        });
    }
});
exports.forgotPassword = forgotPassword;
