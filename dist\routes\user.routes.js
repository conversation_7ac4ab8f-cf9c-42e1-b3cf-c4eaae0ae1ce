"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const user_controller_1 = require("../controllers/user.controller");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = (0, express_1.Router)();
// All user routes require authentication
router.use(auth_middleware_1.verifyToken);
// User profile routes
router.get('/profile', user_controller_1.getUserProfile); // GET /api/user/profile - Get user profile
router.put('/profile', user_controller_1.updateUserProfile); // PUT /api/user/profile - Update user profile
router.delete('/profile', user_controller_1.deleteUserProfile); // DELETE /api/user/profile - Delete user profile
exports.default = router;
