"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRouter = exports.childRouter = exports.authRouter = void 0;
var auth_routes_1 = require("./auth.routes");
Object.defineProperty(exports, "authRouter", { enumerable: true, get: function () { return __importDefault(auth_routes_1).default; } });
var child_routes_1 = require("./child.routes");
Object.defineProperty(exports, "childRouter", { enumerable: true, get: function () { return __importDefault(child_routes_1).default; } });
var user_routes_1 = require("./user.routes");
Object.defineProperty(exports, "userRouter", { enumerable: true, get: function () { return __importDefault(user_routes_1).default; } });
