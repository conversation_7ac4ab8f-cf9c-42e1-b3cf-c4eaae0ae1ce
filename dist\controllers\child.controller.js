"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteChild = exports.getChildDetails = exports.getAllChildren = exports.updateChild = exports.addChild = void 0;
const http_status_codes_1 = require("http-status-codes");
const Child_1 = __importDefault(require("../models/Child"));
const User_1 = __importDefault(require("../models/User"));
// Add a new child (only parents can add children)
const addChild = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const parentId = req.userIdFromToken;
        const { fullname, username, dob, gender, password } = req.body;
        // Verify the user is a parent
        const parent = yield User_1.default.findById(parentId);
        if (!parent || parent.role !== 'Parent') {
            res.status(http_status_codes_1.StatusCodes.FORBIDDEN).json({
                success: false,
                message: 'Only parents can add children'
            });
            return;
        }
        // Check if username already exists (in both User and Child collections)
        const existingUser = yield User_1.default.findOne({ username });
        const existingChild = yield Child_1.default.findOne({ username });
        if (existingUser || existingChild) {
            res.status(http_status_codes_1.StatusCodes.CONFLICT).json({
                success: false,
                message: 'Username already exists'
            });
            return;
        }
        // Create new child
        const newChild = new Child_1.default({
            parent: parentId,
            fullname,
            username,
            dob: new Date(dob),
            gender,
            password,
            role: 'Child'
        });
        yield newChild.save();
        res.status(http_status_codes_1.StatusCodes.CREATED).json({
            success: true,
            message: 'Child added successfully',
            child: {
                id: newChild._id.toString(),
                parent: newChild.parent.toString(),
                fullname: newChild.fullname,
                username: newChild.username,
                dob: newChild.dob,
                gender: newChild.gender,
                role: newChild.role,
                createdAt: newChild.createdAt,
                updatedAt: newChild.updatedAt
            }
        });
    }
    catch (error) {
        console.error('Add child error:', error);
        if (error.code === 11000) {
            res.status(http_status_codes_1.StatusCodes.CONFLICT).json({
                success: false,
                message: 'Username already exists'
            });
            return;
        }
        res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Internal server error while adding child'
        });
    }
});
exports.addChild = addChild;
// Update child (only parent who added the child can update)
const updateChild = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const parentId = req.userIdFromToken;
        const childId = req.params.childId;
        const updateData = req.body;
        // Find the child and verify ownership
        const child = yield Child_1.default.findById(childId);
        if (!child) {
            res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Child not found'
            });
            return;
        }
        // Verify the parent owns this child
        if (child.parent.toString() !== parentId) {
            res.status(http_status_codes_1.StatusCodes.FORBIDDEN).json({
                success: false,
                message: 'You can only update your own children'
            });
            return;
        }
        // Check username uniqueness if username is being updated
        if (updateData.username && updateData.username !== child.username) {
            const existingUser = yield User_1.default.findOne({ username: updateData.username });
            const existingChild = yield Child_1.default.findOne({
                username: updateData.username,
                _id: { $ne: childId }
            });
            if (existingUser || existingChild) {
                res.status(http_status_codes_1.StatusCodes.CONFLICT).json({
                    success: false,
                    message: 'Username already exists'
                });
                return;
            }
        }
        // Update child data
        if (updateData.fullname)
            child.fullname = updateData.fullname;
        if (updateData.username)
            child.username = updateData.username;
        if (updateData.dob)
            child.dob = new Date(updateData.dob);
        if (updateData.gender)
            child.gender = updateData.gender;
        if (updateData.password)
            child.password = updateData.password;
        yield child.save();
        res.status(http_status_codes_1.StatusCodes.OK).json({
            success: true,
            message: 'Child updated successfully',
            child: {
                id: child._id.toString(),
                parent: child.parent.toString(),
                fullname: child.fullname,
                username: child.username,
                dob: child.dob,
                gender: child.gender,
                role: child.role,
                createdAt: child.createdAt,
                updatedAt: child.updatedAt
            }
        });
    }
    catch (error) {
        console.error('Update child error:', error);
        if (error.code === 11000) {
            res.status(http_status_codes_1.StatusCodes.CONFLICT).json({
                success: false,
                message: 'Username already exists'
            });
            return;
        }
        res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Internal server error while updating child'
        });
    }
});
exports.updateChild = updateChild;
// Get all children of a parent
const getAllChildren = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const parentId = req.userIdFromToken;
        // Verify the user is a parent
        const parent = yield User_1.default.findById(parentId);
        if (!parent || parent.role !== 'Parent') {
            res.status(http_status_codes_1.StatusCodes.FORBIDDEN).json({
                success: false,
                message: 'Only parents can view children'
            });
            return;
        }
        // Get all children for this parent
        const children = yield Child_1.default.find({ parent: parentId }).sort({ createdAt: -1 });
        const childrenData = children.map(child => ({
            id: child._id.toString(),
            parent: child.parent.toString(),
            fullname: child.fullname,
            username: child.username,
            dob: child.dob,
            gender: child.gender,
            role: child.role,
            createdAt: child.createdAt,
            updatedAt: child.updatedAt
        }));
        res.status(http_status_codes_1.StatusCodes.OK).json({
            success: true,
            message: 'Children retrieved successfully',
            children: childrenData
        });
    }
    catch (error) {
        console.error('Get all children error:', error);
        res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Internal server error while retrieving children'
        });
    }
});
exports.getAllChildren = getAllChildren;
// Get specific child details
const getChildDetails = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const parentId = req.userIdFromToken;
        const childId = req.params.childId;
        // Find the child and verify ownership
        const child = yield Child_1.default.findById(childId);
        if (!child) {
            res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Child not found'
            });
            return;
        }
        // Verify the parent owns this child
        if (child.parent.toString() !== parentId) {
            res.status(http_status_codes_1.StatusCodes.FORBIDDEN).json({
                success: false,
                message: 'You can only view your own children'
            });
            return;
        }
        res.status(http_status_codes_1.StatusCodes.OK).json({
            success: true,
            message: 'Child details retrieved successfully',
            child: {
                id: child._id.toString(),
                parent: child.parent.toString(),
                fullname: child.fullname,
                username: child.username,
                dob: child.dob,
                gender: child.gender,
                role: child.role,
                createdAt: child.createdAt,
                updatedAt: child.updatedAt
            }
        });
    }
    catch (error) {
        console.error('Get child details error:', error);
        res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Internal server error while retrieving child details'
        });
    }
});
exports.getChildDetails = getChildDetails;
// Delete a child (only parent who added can delete)
const deleteChild = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const parentId = req.userIdFromToken;
        const childId = req.params.childId;
        // Find the child and verify ownership
        const child = yield Child_1.default.findById(childId);
        if (!child) {
            res.status(http_status_codes_1.StatusCodes.NOT_FOUND).json({
                success: false,
                message: 'Child not found'
            });
            return;
        }
        // Verify the parent owns this child
        if (child.parent.toString() !== parentId) {
            res.status(http_status_codes_1.StatusCodes.FORBIDDEN).json({
                success: false,
                message: 'You can only delete your own children'
            });
            return;
        }
        // Delete the child
        yield Child_1.default.findByIdAndDelete(childId);
        res.status(http_status_codes_1.StatusCodes.OK).json({
            success: true,
            message: 'Child deleted successfully'
        });
    }
    catch (error) {
        console.error('Delete child error:', error);
        res.status(http_status_codes_1.StatusCodes.INTERNAL_SERVER_ERROR).json({
            success: false,
            message: 'Internal server error while deleting child'
        });
    }
});
exports.deleteChild = deleteChild;
