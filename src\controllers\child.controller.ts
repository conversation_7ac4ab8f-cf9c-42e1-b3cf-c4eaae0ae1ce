import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import Child from '../models/Child';
import User from '../models/User';
import { IAddChildRequest, IUpdateChildRequest, IChildResponse } from '../interfaces/child.interface';

// Add a new child (only parents can add children)
export const addChild = async (req: Request, res: Response): Promise<void> => {
  try {
    const parentId = req.userIdFromToken;
    const { fullname, username, dob, gender, password }: IAddChildRequest = req.body;

    // Verify the user is a parent
    const parent = await User.findById(parentId);
    if (!parent || parent.role !== 'Parent') {
      res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Only parents can add children'
      } as IChildResponse);
      return;
    }

    
    // Check if username already exists (in both User and Child collections)
    const existingUser = await User.findOne({ username : username.toLowerCase() });
    const existingChild = await Child.findOne({ username: username.toLowerCase() });
    
    if (existingUser || existingChild) {
      res.status(StatusCodes.CONFLICT).json({
        success: false,
        message: 'Username already exists'
      } as IChildResponse);
      return;
    }

    // Create new child
    const newChild = new Child({
      parent: parentId,
      fullname,
      username: username.toLowerCase(),
      dob: new Date(dob),
      gender,
      password,
      role: 'Child'
    });

    await newChild.save();

    res.status(StatusCodes.CREATED).json({
      success: true,
      message: 'Child added successfully',
      child: {
        id: newChild._id.toString(),
        parent: newChild.parent.toString(),
        fullname: newChild.fullname,
        username: newChild.username,
        dob: newChild.dob,
        gender: newChild.gender,
        role: newChild.role,
        createdAt: newChild.createdAt,
        updatedAt: newChild.updatedAt
      }
    } as IChildResponse);

  } catch (error: any) {
    console.error('Add child error:', error);
    
    if (error.code === 11000) {
      res.status(StatusCodes.CONFLICT).json({
        success: false,
        message: 'Username already exists'
      } as IChildResponse);
      return;
    }

    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error while adding child'
    } as IChildResponse);
  }
};

// Update child (only parent who added the child can update)
export const updateChild = async (req: Request, res: Response): Promise<void> => {
  try {
    const parentId = req.userIdFromToken;
    const childId = req.params.childId;
    const updateData: IUpdateChildRequest = req.body;

    // Find the child and verify ownership
    const child = await Child.findById(childId);
    if (!child) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Child not found'
      } as IChildResponse);
      return;
    }

    // Verify the parent owns this child
    if (child.parent.toString() !== parentId) {
      res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'You can only update your own children'
      } as IChildResponse);
      return;
    }

    // Check username uniqueness if username is being updated
    if (updateData.username?.toLowerCase() && updateData.username !== child.username) {
      const existingUser = await User.findOne({ username: updateData.username });
      const existingChild = await Child.findOne({ 
        username: updateData.username.toLowerCase(),
        _id: { $ne: childId }
      });
      
      if (existingUser || existingChild) {
        res.status(StatusCodes.CONFLICT).json({
          success: false,
          message: 'Username already exists'
        } as IChildResponse);
        return;
      }
    }

    // Update child data
    if (updateData.fullname) child.fullname = updateData.fullname;
    if (updateData.username) child.username = updateData.username;
    if (updateData.dob) child.dob = new Date(updateData.dob);
    if (updateData.gender) child.gender = updateData.gender;
    if (updateData.password) child.password = updateData.password;

    await child.save();

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Child updated successfully',
      child: {
        id: child._id.toString(),
        parent: child.parent.toString(),
        fullname: child.fullname,
        username: child.username.toLowerCase(),
        dob: child.dob,
        gender: child.gender,
        role: child.role,
        createdAt: child.createdAt,
        updatedAt: child.updatedAt
      }
    } as IChildResponse);

  } catch (error: any) {
    console.error('Update child error:', error);

    if (error.code === 11000) {
      res.status(StatusCodes.CONFLICT).json({
        success: false,
        message: 'Username already exists'
      } as IChildResponse);
      return;
    }

    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error while updating child'
    } as IChildResponse);
  }
};

// Get all children of a parent
export const getAllChildren = async (req: Request, res: Response): Promise<void> => {
  try {
    const parentId = req.userIdFromToken;

    // Verify the user is a parent
    const parent = await User.findById(parentId);
    if (!parent || parent.role !== 'Parent') {
      res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Only parents can view children'
      } as IChildResponse);
      return;
    }

    // Get all children for this parent
    const children = await Child.find({ parent: parentId }).sort({ createdAt: -1 });

    const childrenData = children.map(child => ({
      id: child._id.toString(),
      parent: child.parent.toString(),
      fullname: child.fullname,
      username: child.username,
      dob: child.dob,
      gender: child.gender,
      role: child.role,
      createdAt: child.createdAt,
      updatedAt: child.updatedAt
    }));

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Children retrieved successfully',
      children: childrenData
    } as IChildResponse);

  } catch (error) {
    console.error('Get all children error:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error while retrieving children'
    } as IChildResponse);
  }
};

// Get specific child details
export const getChildDetails = async (req: Request, res: Response): Promise<void> => {
  try {
    const parentId = req.userIdFromToken;
    const childId = req.params.childId;

    // Find the child and verify ownership
    const child = await Child.findById(childId);
    if (!child) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Child not found'
      } as IChildResponse);
      return;
    }

    // Verify the parent owns this child
    if (child.parent.toString() !== parentId) {
      res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'You can only view your own children'
      } as IChildResponse);
      return;
    }

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Child details retrieved successfully',
      child: {
        id: child._id.toString(),
        parent: child.parent.toString(),
        fullname: child.fullname,
        username: child.username,
        dob: child.dob,
        gender: child.gender,
        role: child.role,
        createdAt: child.createdAt,
        updatedAt: child.updatedAt
      }
    } as IChildResponse);

  } catch (error) {
    console.error('Get child details error:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error while retrieving child details'
    } as IChildResponse);
  }
};

// Delete a child (only parent who added can delete)
export const deleteChild = async (req: Request, res: Response): Promise<void> => {
  try {
    const parentId = req.userIdFromToken;
    const childId = req.params.childId;

    // Find the child and verify ownership
    const child = await Child.findById(childId);
    if (!child) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Child not found'
      } as IChildResponse);
      return;
    }

    // Verify the parent owns this child
    if (child.parent.toString() !== parentId) {
      res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'You can only delete your own children'
      } as IChildResponse);
      return;
    }

    // Delete the child
    await Child.findByIdAndDelete(childId);

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Child deleted successfully'
    } as IChildResponse);

  } catch (error) {
    console.error('Delete child error:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error while deleting child'
    } as IChildResponse);
  }
};
