import { Document, Types } from 'mongoose';

export interface IChild extends Document {
  _id: Types.ObjectId;
  parent: Types.ObjectId; // Reference to User (Parent)
  fullname: string;
  username: string;
  dob: Date;
  gender: 'Male' | 'Female' | 'Other';
  password: string;
  role: 'Child';
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

export interface IAddChildRequest {
  fullname: string;
  username: string;
  dob: Date;
  gender: 'Male' | 'Female' | 'Other';
  password: string;
}

export interface IUpdateChildRequest {
  fullname?: string;
  username?: string;
  dob?: Date;
  gender?: 'Male' | 'Female' | 'Other';
  password?: string;
}

export interface IChildResponse {
  success: boolean;
  message: string;
  child?: {
    id: string;
    parent: string;
    fullname: string;
    username: string;
    dob: Date;
    gender: string;
    role: string;
    createdAt: Date;
    updatedAt: Date;
  };
  children?: Array<{
    id: string;
    parent: string;
    fullname: string;
    username: string;
    dob: Date;
    gender: string;
    role: string;
    createdAt: Date;
    updatedAt: Date;
  }>;
}
