"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const child_controller_1 = require("../controllers/child.controller");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = (0, express_1.Router)();
// All child routes require authentication
router.use(auth_middleware_1.verifyToken);
// Child CRUD Routes
router.post('/', child_controller_1.addChild); // POST /api/child - Add a new child
router.get('/', child_controller_1.getAllChildren); // GET /api/child - Get all children of parent
router.get('/:childId', child_controller_1.getChildDetails); // GET /api/child/:childId - Get specific child details
router.put('/:childId', child_controller_1.updateChild); // PUT /api/child/:childId - Update child
router.delete('/:childId', child_controller_1.deleteChild); // DELETE /api/child/:childId - Delete child
exports.default = router;
