import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import User from '../models/User';
import Child from '../models/Child';
import {
  IUserResponse,
  IUpdateUserRequest,
  IDeleteUserRequest
} from '../interfaces/user.interface'

// Get user profile (parent)
export const getUserProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.userIdFromToken;
    console.log("userID in getUserProfile", userId);

    const user = await User.findById(userId);
    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      } as IUserResponse);
      return;
    }

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Profile retrieved successfully',
      user: {
        id: user._id.toString(),
        fullname: user.fullname,
        username: user.username,
        email: user.email,
        phoneNumber: user.phoneNumber,
        role: user.role,
        isVerified: user.isVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    } as IUserResponse);

  } catch (error) {
    console.error('Get user profile error:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error while retrieving profile'
    } as IUserResponse);
  }
};

// Update user profile
export const updateUserProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.userIdFromToken;
    const { fullname, email, phoneNumber }: IUpdateUserRequest = req.body;

    const user = await User.findById(userId);
    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      } as IUserResponse);
      return;
    }

    // Check if email is being updated and if it already exists
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ 
        email: email,
        _id: { $ne: userId }
      });
      
      if (existingUser) {
        res.status(StatusCodes.CONFLICT).json({
          success: false,
          message: 'Email already exists'
        } as IUserResponse);
        return;
      }
    }

    // Update user data
    if (fullname) user.fullname = fullname;
    if (email) user.email = email;
    if (phoneNumber) user.phoneNumber = phoneNumber;

    await user.save();

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Profile updated successfully',
      user: {
        id: user._id.toString(),
        fullname: user.fullname,
        username: user.username,
        email: user.email,
        phoneNumber: user.phoneNumber,
        role: user.role,
        isVerified: user.isVerified,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    } as IUserResponse);

  } catch (error: any) {
    console.error('Update user profile error:', error);
    
    if (error.code === 11000) {
      res.status(StatusCodes.CONFLICT).json({
        success: false,
        message: 'Email already exists'
      } as IUserResponse);
      return;
    }

    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error while updating profile'
    } as IUserResponse);
  }
};

// Delete user profile (with password confirmation and cascade delete children)
export const deleteUserProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.userIdFromToken;
    const { password }: IDeleteUserRequest = req.body;

    if (!password) {
      res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Password is required for account deletion'
      } as IUserResponse);
      return;
    }

    const user = await User.findById(userId);
    if (!user) {
      res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      } as IUserResponse);
      return;
    }

    // Verify password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'Invalid password'
      } as IUserResponse);
      return;
    }

    // Delete all children of this parent first (cascade delete)
    await Child.deleteMany({ parent: userId });

    // Delete the user
    await User.findByIdAndDelete(userId);

    res.status(StatusCodes.OK).json({
      success: true,
      message: 'Profile and all associated children deleted successfully'
    } as IUserResponse);

  } catch (error) {
    console.error('Delete user profile error:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error while deleting profile'
    } as IUserResponse);
  }
};
